import {
  require_client
} from "./chunk-7XCV7ATQ.js";
import "./chunk-E7Q22A2S.js";
import {
  require_react
} from "./chunk-6P6Q65E3.js";
import {
  __toESM
} from "./chunk-5WRI5ZAA.js";

// node_modules/@astrojs/react/dist/client.js
var import_react2 = __toESM(require_react());
var import_client = __toESM(require_client());

// node_modules/@astrojs/react/dist/static-html.js
var import_react = __toESM(require_react(), 1);
var StaticHtml = ({
  value,
  name,
  hydrate = true
}) => {
  if (!value) return null;
  const tagName = hydrate ? "astro-slot" : "astro-static-slot";
  return (0, import_react.createElement)(tagName, {
    name,
    suppressHydrationWarning: true,
    dangerouslySetInnerHTML: { __html: value }
  });
};
StaticHtml.shouldComponentUpdate = () => false;
var static_html_default = StaticHtml;

// node_modules/@astrojs/react/dist/client.js
function isAlreadyHydrated(element) {
  for (const key in element) {
    if (key.startsWith("__reactContainer")) {
      return key;
    }
  }
}
function createReactElementFromDOMElement(element) {
  let attrs = {};
  for (const attr of element.attributes) {
    attrs[attr.name] = attr.value;
  }
  if (element.firstChild === null) {
    return (0, import_react2.createElement)(element.localName, attrs);
  }
  return (0, import_react2.createElement)(
    element.localName,
    attrs,
    Array.from(element.childNodes).map((c) => {
      if (c.nodeType === Node.TEXT_NODE) {
        return c.data;
      } else if (c.nodeType === Node.ELEMENT_NODE) {
        return createReactElementFromDOMElement(c);
      } else {
        return void 0;
      }
    }).filter((a) => !!a)
  );
}
function getChildren(childString, experimentalReactChildren) {
  if (experimentalReactChildren && childString) {
    let children = [];
    let template = document.createElement("template");
    template.innerHTML = childString;
    for (let child of template.content.children) {
      children.push(createReactElementFromDOMElement(child));
    }
    return children;
  } else if (childString) {
    return (0, import_react2.createElement)(static_html_default, { value: childString });
  } else {
    return void 0;
  }
}
var rootMap = /* @__PURE__ */ new WeakMap();
var getOrCreateRoot = (element, creator) => {
  let root = rootMap.get(element);
  if (!root) {
    root = creator();
    rootMap.set(element, root);
  }
  return root;
};
var client_default = (element) => (Component, props, { default: children, ...slotted }, { client }) => {
  if (!element.hasAttribute("ssr")) return;
  const actionKey = element.getAttribute("data-action-key");
  const actionName = element.getAttribute("data-action-name");
  const stringifiedActionResult = element.getAttribute("data-action-result");
  const formState = actionKey && actionName && stringifiedActionResult ? [JSON.parse(stringifiedActionResult), actionKey, actionName] : void 0;
  const renderOptions = {
    identifierPrefix: element.getAttribute("prefix"),
    formState
  };
  for (const [key, value] of Object.entries(slotted)) {
    props[key] = (0, import_react2.createElement)(static_html_default, { value, name: key });
  }
  const componentEl = (0, import_react2.createElement)(
    Component,
    props,
    getChildren(children, element.hasAttribute("data-react-children"))
  );
  const rootKey = isAlreadyHydrated(element);
  if (rootKey) {
    delete element[rootKey];
  }
  if (client === "only") {
    return (0, import_react2.startTransition)(() => {
      const root = getOrCreateRoot(element, () => {
        const r = (0, import_client.createRoot)(element);
        element.addEventListener("astro:unmount", () => r.unmount(), { once: true });
        return r;
      });
      root.render(componentEl);
    });
  }
  (0, import_react2.startTransition)(() => {
    const root = getOrCreateRoot(element, () => {
      const r = (0, import_client.hydrateRoot)(element, componentEl, renderOptions);
      element.addEventListener("astro:unmount", () => r.unmount(), { once: true });
      return r;
    });
    root.render(componentEl);
  });
};
export {
  client_default as default
};
//# sourceMappingURL=@astrojs_react_client__js.js.map
