{"version": 3, "sources": ["../../@astrojs/react/dist/client.js", "../../@astrojs/react/dist/static-html.js"], "sourcesContent": ["import { createElement, startTransition } from \"react\";\nimport { createRoot, hydrateRoot } from \"react-dom/client\";\nimport StaticHtml from \"./static-html.js\";\nfunction isAlreadyHydrated(element) {\n  for (const key in element) {\n    if (key.startsWith(\"__reactContainer\")) {\n      return key;\n    }\n  }\n}\nfunction createReactElementFromDOMElement(element) {\n  let attrs = {};\n  for (const attr of element.attributes) {\n    attrs[attr.name] = attr.value;\n  }\n  if (element.firstChild === null) {\n    return createElement(element.localName, attrs);\n  }\n  return createElement(\n    element.localName,\n    attrs,\n    Array.from(element.childNodes).map((c) => {\n      if (c.nodeType === Node.TEXT_NODE) {\n        return c.data;\n      } else if (c.nodeType === Node.ELEMENT_NODE) {\n        return createReactElementFromDOMElement(c);\n      } else {\n        return void 0;\n      }\n    }).filter((a) => !!a)\n  );\n}\nfunction getChildren(childString, experimentalReactChildren) {\n  if (experimentalReactChildren && childString) {\n    let children = [];\n    let template = document.createElement(\"template\");\n    template.innerHTML = childString;\n    for (let child of template.content.children) {\n      children.push(createReactElementFromDOMElement(child));\n    }\n    return children;\n  } else if (childString) {\n    return createElement(StaticHtml, { value: childString });\n  } else {\n    return void 0;\n  }\n}\nlet rootMap = /* @__PURE__ */ new WeakMap();\nconst getOrCreateRoot = (element, creator) => {\n  let root = rootMap.get(element);\n  if (!root) {\n    root = creator();\n    rootMap.set(element, root);\n  }\n  return root;\n};\nvar client_default = (element) => (Component, props, { default: children, ...slotted }, { client }) => {\n  if (!element.hasAttribute(\"ssr\")) return;\n  const actionKey = element.getAttribute(\"data-action-key\");\n  const actionName = element.getAttribute(\"data-action-name\");\n  const stringifiedActionResult = element.getAttribute(\"data-action-result\");\n  const formState = actionKey && actionName && stringifiedActionResult ? [JSON.parse(stringifiedActionResult), actionKey, actionName] : void 0;\n  const renderOptions = {\n    identifierPrefix: element.getAttribute(\"prefix\"),\n    formState\n  };\n  for (const [key, value] of Object.entries(slotted)) {\n    props[key] = createElement(StaticHtml, { value, name: key });\n  }\n  const componentEl = createElement(\n    Component,\n    props,\n    getChildren(children, element.hasAttribute(\"data-react-children\"))\n  );\n  const rootKey = isAlreadyHydrated(element);\n  if (rootKey) {\n    delete element[rootKey];\n  }\n  if (client === \"only\") {\n    return startTransition(() => {\n      const root = getOrCreateRoot(element, () => {\n        const r = createRoot(element);\n        element.addEventListener(\"astro:unmount\", () => r.unmount(), { once: true });\n        return r;\n      });\n      root.render(componentEl);\n    });\n  }\n  startTransition(() => {\n    const root = getOrCreateRoot(element, () => {\n      const r = hydrateRoot(element, componentEl, renderOptions);\n      element.addEventListener(\"astro:unmount\", () => r.unmount(), { once: true });\n      return r;\n    });\n    root.render(componentEl);\n  });\n};\nexport {\n  client_default as default\n};\n", "import { createElement as h } from \"react\";\nconst StaticHtml = ({\n  value,\n  name,\n  hydrate = true\n}) => {\n  if (!value) return null;\n  const tagName = hydrate ? \"astro-slot\" : \"astro-static-slot\";\n  return h(tagName, {\n    name,\n    suppressHydrationWarning: true,\n    dangerouslySetInnerHTML: { __html: value }\n  });\n};\nStaticHtml.shouldComponentUpdate = () => false;\nvar static_html_default = StaticHtml;\nexport {\n  static_html_default as default\n};\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,gBAA+C;AAC/C,oBAAwC;;;ACDxC,mBAAmC;AACnC,IAAM,aAAa,CAAC;AAAA,EAClB;AAAA,EACA;AAAA,EACA,UAAU;AACZ,MAAM;AACJ,MAAI,CAAC,MAAO,QAAO;AACnB,QAAM,UAAU,UAAU,eAAe;AACzC,aAAO,aAAAC,eAAE,SAAS;AAAA,IAChB;AAAA,IACA,0BAA0B;AAAA,IAC1B,yBAAyB,EAAE,QAAQ,MAAM;AAAA,EAC3C,CAAC;AACH;AACA,WAAW,wBAAwB,MAAM;AACzC,IAAI,sBAAsB;;;ADZ1B,SAAS,kBAAkB,SAAS;AAClC,aAAW,OAAO,SAAS;AACzB,QAAI,IAAI,WAAW,kBAAkB,GAAG;AACtC,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,iCAAiC,SAAS;AACjD,MAAI,QAAQ,CAAC;AACb,aAAW,QAAQ,QAAQ,YAAY;AACrC,UAAM,KAAK,IAAI,IAAI,KAAK;AAAA,EAC1B;AACA,MAAI,QAAQ,eAAe,MAAM;AAC/B,eAAO,6BAAc,QAAQ,WAAW,KAAK;AAAA,EAC/C;AACA,aAAO;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,IACA,MAAM,KAAK,QAAQ,UAAU,EAAE,IAAI,CAAC,MAAM;AACxC,UAAI,EAAE,aAAa,KAAK,WAAW;AACjC,eAAO,EAAE;AAAA,MACX,WAAW,EAAE,aAAa,KAAK,cAAc;AAC3C,eAAO,iCAAiC,CAAC;AAAA,MAC3C,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAAA,EACtB;AACF;AACA,SAAS,YAAY,aAAa,2BAA2B;AAC3D,MAAI,6BAA6B,aAAa;AAC5C,QAAI,WAAW,CAAC;AAChB,QAAI,WAAW,SAAS,cAAc,UAAU;AAChD,aAAS,YAAY;AACrB,aAAS,SAAS,SAAS,QAAQ,UAAU;AAC3C,eAAS,KAAK,iCAAiC,KAAK,CAAC;AAAA,IACvD;AACA,WAAO;AAAA,EACT,WAAW,aAAa;AACtB,eAAO,6BAAc,qBAAY,EAAE,OAAO,YAAY,CAAC;AAAA,EACzD,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAI,UAA0B,oBAAI,QAAQ;AAC1C,IAAM,kBAAkB,CAAC,SAAS,YAAY;AAC5C,MAAI,OAAO,QAAQ,IAAI,OAAO;AAC9B,MAAI,CAAC,MAAM;AACT,WAAO,QAAQ;AACf,YAAQ,IAAI,SAAS,IAAI;AAAA,EAC3B;AACA,SAAO;AACT;AACA,IAAI,iBAAiB,CAAC,YAAY,CAAC,WAAW,OAAO,EAAE,SAAS,UAAU,GAAG,QAAQ,GAAG,EAAE,OAAO,MAAM;AACrG,MAAI,CAAC,QAAQ,aAAa,KAAK,EAAG;AAClC,QAAM,YAAY,QAAQ,aAAa,iBAAiB;AACxD,QAAM,aAAa,QAAQ,aAAa,kBAAkB;AAC1D,QAAM,0BAA0B,QAAQ,aAAa,oBAAoB;AACzE,QAAM,YAAY,aAAa,cAAc,0BAA0B,CAAC,KAAK,MAAM,uBAAuB,GAAG,WAAW,UAAU,IAAI;AACtI,QAAM,gBAAgB;AAAA,IACpB,kBAAkB,QAAQ,aAAa,QAAQ;AAAA,IAC/C;AAAA,EACF;AACA,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,OAAO,GAAG;AAClD,UAAM,GAAG,QAAI,6BAAc,qBAAY,EAAE,OAAO,MAAM,IAAI,CAAC;AAAA,EAC7D;AACA,QAAM,kBAAc;AAAA,IAClB;AAAA,IACA;AAAA,IACA,YAAY,UAAU,QAAQ,aAAa,qBAAqB,CAAC;AAAA,EACnE;AACA,QAAM,UAAU,kBAAkB,OAAO;AACzC,MAAI,SAAS;AACX,WAAO,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,WAAW,QAAQ;AACrB,eAAO,+BAAgB,MAAM;AAC3B,YAAM,OAAO,gBAAgB,SAAS,MAAM;AAC1C,cAAM,QAAI,0BAAW,OAAO;AAC5B,gBAAQ,iBAAiB,iBAAiB,MAAM,EAAE,QAAQ,GAAG,EAAE,MAAM,KAAK,CAAC;AAC3E,eAAO;AAAA,MACT,CAAC;AACD,WAAK,OAAO,WAAW;AAAA,IACzB,CAAC;AAAA,EACH;AACA,qCAAgB,MAAM;AACpB,UAAM,OAAO,gBAAgB,SAAS,MAAM;AAC1C,YAAM,QAAI,2BAAY,SAAS,aAAa,aAAa;AACzD,cAAQ,iBAAiB,iBAAiB,MAAM,EAAE,QAAQ,GAAG,EAAE,MAAM,KAAK,CAAC;AAC3E,aAAO;AAAA,IACT,CAAC;AACD,SAAK,OAAO,WAAW;AAAA,EACzB,CAAC;AACH;", "names": ["import_react", "h"]}