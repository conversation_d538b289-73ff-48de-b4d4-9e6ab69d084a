import React, { useState, useEffect } from 'react';

interface Member {
  id: number;
  name: string;
  position: string;
  specialty: string;
  institution: string;
  bio: string;
  email: string;
  image: string;
}

interface Project {
  id: number;
  title: string;
  location: string;
  status: string;
  leadInvestigator: string;
  startDate: string;
  endDate: string;
  description: string;
  participants: number;
  budget: string;
  outcomes: string;
}

interface Event {
  id: number;
  title: string;
  date: string;
  time: string;
  location: string;
  description: string;
  registrationLink: string;
}

const AdminPanel: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('content');
  const [members, setMembers] = useState<Member[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [siteContent, setSiteContent] = useState<any>({});

  // Authentication state
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [loginError, setLoginError] = useState('');

  // Check authentication status on component mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth');
      const data = await response.json();
      setIsAuthenticated(data.authenticated);
    } catch (error) {
      console.error('Auth check failed:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError('');
    
    try {
      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginForm),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setIsAuthenticated(true);
        loadData();
      } else {
        setLoginError(data.message);
      }
    } catch (error) {
      setLoginError('Login failed. Please try again.');
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth', { method: 'DELETE' });
      setIsAuthenticated(false);
      setLoginForm({ username: '', password: '' });
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const loadData = async () => {
    try {
      // Load members
      const membersResponse = await fetch('/api/members');
      const membersData = await membersResponse.json();
      setMembers(membersData.boardMembers || []);

      // Load projects
      const projectsResponse = await fetch('/api/projects');
      const projectsData = await projectsResponse.json();
      setProjects(projectsData.globalHealthProjects || []);

      // Load content
      const contentResponse = await fetch('/api/content');
      const contentData = await contentResponse.json();
      setSiteContent(contentData);
      setEvents(contentData.site?.upcomingEvents || []);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      loadData();
    }
  }, [isAuthenticated]);



  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="glass-card p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
          <p className="text-white mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '80vh',
        padding: '20px'
      }}>
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255,255,255,0.2)',
          borderRadius: '16px',
          padding: '32px',
          maxWidth: '400px',
          width: '100%',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
        }}>
          <h2 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'white',
            marginBottom: '24px',
            textAlign: 'center'
          }}>
            Admin Login
          </h2>
          <form onSubmit={handleLogin} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div>
              <label style={{
                color: 'white',
                fontWeight: '500',
                marginBottom: '8px',
                display: 'block'
              }}>
                Username
              </label>
              <input
                type="text"
                value={loginForm.username}
                onChange={(e) => setLoginForm({ ...loginForm, username: e.target.value })}
                placeholder="Enter username"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '16px'
                }}
              />
            </div>
            <div>
              <label style={{
                color: 'white',
                fontWeight: '500',
                marginBottom: '8px',
                display: 'block'
              }}>
                Password
              </label>
              <input
                type="password"
                value={loginForm.password}
                onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                placeholder="Enter password"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '16px'
                }}
              />
            </div>
            {loginError && (
              <div style={{
                background: 'rgba(239, 68, 68, 0.2)',
                color: '#fca5a5',
                padding: '12px',
                borderRadius: '8px',
                fontSize: '14px'
              }}>
                {loginError}
              </div>
            )}
            <button
              type="submit"
              style={{
                width: '100%',
                padding: '12px 24px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                fontWeight: '600',
                fontSize: '16px',
                cursor: 'pointer',
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
                transition: 'transform 0.2s'
              }}
              onMouseOver={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
              onMouseOut={(e) => e.currentTarget.style.transform = 'scale(1)'}
            >
              Login
            </button>
          </form>
          <div style={{
            marginTop: '24px',
            padding: '16px',
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '8px'
          }}>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              fontSize: '14px',
              textAlign: 'center',
              lineHeight: '1.5'
            }}>
              Demo Credentials:<br />
              Username: <span style={{ color: 'white', fontFamily: 'monospace' }}>admin</span><br />
              Password: <span style={{ color: 'white', fontFamily: 'monospace' }}>password</span>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '32px 16px' }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        borderRadius: '16px',
        padding: '32px',
        marginBottom: '32px',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '24px',
          flexWrap: 'wrap',
          gap: '16px'
        }}>
          <h1 style={{
            fontSize: '32px',
            fontWeight: 'bold',
            color: 'white',
            margin: 0
          }}>
            Admin Panel
          </h1>
          <button
            onClick={handleLogout}
            style={{
              padding: '8px 16px',
              background: 'rgba(255,255,255,0.1)',
              border: '1px solid rgba(255,255,255,0.2)',
              borderRadius: '20px',
              color: 'white',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            Logout
          </button>
        </div>

        {/* Tab Navigation */}
        <div style={{
          display: 'flex',
          gap: '16px',
          marginBottom: '32px',
          overflowX: 'auto',
          paddingBottom: '8px'
        }}>
          {[
            { id: 'content', label: 'Site Content' },
            { id: 'members', label: 'Board Members' },
            { id: 'projects', label: 'Global Health Projects' },
            { id: 'events', label: 'Events' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              style={{
                padding: '8px 16px',
                borderRadius: '8px',
                fontWeight: '500',
                whiteSpace: 'nowrap',
                border: 'none',
                cursor: 'pointer',
                transition: 'all 0.2s',
                background: activeTab === tab.id ? '#667eea' : 'transparent',
                color: activeTab === tab.id ? 'white' : 'rgba(255,255,255,0.7)'
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div style={{ minHeight: '400px' }}>
          {activeTab === 'content' && <ContentManager content={siteContent} />}
          {activeTab === 'members' && <MembersManager members={members} setMembers={setMembers} />}
          {activeTab === 'projects' && <ProjectsManager projects={projects} setProjects={setProjects} />}
          {activeTab === 'events' && <EventsManager events={events} setEvents={setEvents} />}
        </div>
      </div>
    </div>
  );
};

// Content Manager Component
const ContentManager: React.FC<{ content: any }> = ({ content }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
        Site Content Management
      </h2>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <p style={{ color: 'rgba(255,255,255,0.8)', marginBottom: '16px' }}>
          Content management features will be implemented here. This includes:
        </p>
        <ul style={{
          color: 'rgba(255,255,255,0.7)',
          paddingLeft: '20px',
          lineHeight: '1.6'
        }}>
          <li style={{ marginBottom: '8px' }}>Edit hero section content</li>
          <li style={{ marginBottom: '8px' }}>Update organization description</li>
          <li style={{ marginBottom: '8px' }}>Manage navigation links</li>
          <li style={{ marginBottom: '8px' }}>Update admin resources</li>
        </ul>
      </div>
    </div>
  );
};

// Members Manager Component
const MembersManager: React.FC<{ members: Member[]; setMembers: (members: Member[]) => void }> = ({
  members,
  setMembers
}) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Board Members Management
        </h2>
        <button style={{
          padding: '8px 16px',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          border: 'none',
          borderRadius: '20px',
          fontWeight: '600',
          cursor: 'pointer',
          fontSize: '14px'
        }}>
          Add New Member
        </button>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px'
      }}>
        {members.map((member) => (
          <div key={member.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: 'white',
              marginBottom: '8px'
            }}>
              {member.name}
            </h3>
            <p style={{
              color: '#a5b8ff',
              fontSize: '14px',
              marginBottom: '8px'
            }}>
              {member.position}
            </p>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              fontSize: '14px',
              marginBottom: '8px'
            }}>
              {member.specialty}
            </p>
            <p style={{
              color: 'rgba(255,255,255,0.6)',
              fontSize: '14px',
              marginBottom: '16px'
            }}>
              {member.institution}
            </p>
            <div style={{ display: 'flex', gap: '8px' }}>
              <button style={{
                padding: '4px 12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '4px',
                color: 'white',
                cursor: 'pointer',
                fontSize: '12px'
              }}>
                Edit
              </button>
              <button style={{
                padding: '4px 12px',
                background: 'rgba(239, 68, 68, 0.2)',
                border: '1px solid rgba(239, 68, 68, 0.3)',
                borderRadius: '4px',
                color: '#fca5a5',
                cursor: 'pointer',
                fontSize: '12px'
              }}>
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Projects Manager Component
const ProjectsManager: React.FC<{ projects: Project[]; setProjects: (projects: Project[]) => void }> = ({
  projects,
  setProjects
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return { bg: 'rgba(34, 197, 94, 0.2)', color: '#86efac' };
      case 'planning': return { bg: 'rgba(234, 179, 8, 0.2)', color: '#fde047' };
      case 'completed': return { bg: 'rgba(59, 130, 246, 0.2)', color: '#93c5fd' };
      default: return { bg: 'rgba(34, 197, 94, 0.2)', color: '#86efac' };
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Global Health Projects
        </h2>
        <button style={{
          padding: '8px 16px',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          border: 'none',
          borderRadius: '20px',
          fontWeight: '600',
          cursor: 'pointer',
          fontSize: '14px'
        }}>
          Add New Project
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {projects.map((project) => (
          <div key={project.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: '12px'
                }}>
                  {project.title}
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                  gap: '16px',
                  fontSize: '14px'
                }}>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Location</p>
                    <p style={{ color: 'white' }}>{project.location}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Status</p>
                    <span style={{
                      ...getStatusColor(project.status),
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '500'
                    }}>
                      {project.status}
                    </span>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Lead</p>
                    <p style={{ color: 'white' }}>{project.leadInvestigator}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Budget</p>
                    <p style={{ color: '#a5b8ff' }}>{project.budget}</p>
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button style={{
                  padding: '4px 12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '4px',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}>
                  Edit
                </button>
                <button style={{
                  padding: '4px 12px',
                  background: 'rgba(239, 68, 68, 0.2)',
                  border: '1px solid rgba(239, 68, 68, 0.3)',
                  borderRadius: '4px',
                  color: '#fca5a5',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}>
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Events Manager Component
const EventsManager: React.FC<{ events: Event[]; setEvents: (events: Event[]) => void }> = ({
  events,
  setEvents
}) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Upcoming Events
        </h2>
        <button style={{
          padding: '8px 16px',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          border: 'none',
          borderRadius: '20px',
          fontWeight: '600',
          cursor: 'pointer',
          fontSize: '14px'
        }}>
          Add New Event
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {events.map((event) => (
          <div key={event.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: '12px'
                }}>
                  {event.title}
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '16px',
                  fontSize: '14px',
                  marginBottom: '12px'
                }}>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Date & Time</p>
                    <p style={{ color: 'white' }}>
                      {new Date(event.date).toLocaleDateString()} at {event.time}
                    </p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Location</p>
                    <p style={{ color: 'white' }}>{event.location}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Registration</p>
                    <a
                      href={event.registrationLink}
                      style={{ color: '#a5b8ff', textDecoration: 'none' }}
                      onMouseOver={(e) => e.currentTarget.style.color = '#c7d6ff'}
                      onMouseOut={(e) => e.currentTarget.style.color = '#a5b8ff'}
                    >
                      View Link
                    </a>
                  </div>
                </div>
                <p style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>
                  {event.description}
                </p>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button style={{
                  padding: '4px 12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '4px',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}>
                  Edit
                </button>
                <button style={{
                  padding: '4px 12px',
                  background: 'rgba(239, 68, 68, 0.2)',
                  border: '1px solid rgba(239, 68, 68, 0.3)',
                  borderRadius: '4px',
                  color: '#fca5a5',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}>
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AdminPanel;
